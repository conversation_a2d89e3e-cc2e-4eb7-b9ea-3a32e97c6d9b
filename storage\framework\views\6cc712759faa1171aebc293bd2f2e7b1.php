<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            <div class="flex items-center space-x-2">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('heroicon-m-calendar-days'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(BladeUI\Icons\Components\Svg::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-blue-500']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                <span>Timeline Kegiatan</span>
            </div>
         <?php $__env->endSlot(); ?>

        <!--[if BLOCK]><![endif]--><?php if($timelineData['projectsData']->isNotEmpty()): ?>
            <div class="space-y-4">
                <!-- Timeline Header -->
                <div class="relative">
                    <div class="flex border-b border-gray-200 dark:border-gray-700 pb-2">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $timelineData['months']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="text-center text-sm font-medium text-gray-600 dark:text-gray-400"
                                 style="width: <?php echo e($month['widthPercent']); ?>%">
                                <?php echo e($month['name']); ?>

                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Current Date Indicator -->
                    <?php
                        $currentDateOffset = $currentDate->diffInDays($timelineData['startDate']);
                        $currentDatePercent = ($currentDateOffset / $timelineData['totalDays']) * 100;
                    ?>
                    <!--[if BLOCK]><![endif]--><?php if($currentDatePercent >= 0 && $currentDatePercent <= 100): ?>
                        <div class="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10"
                             style="left: <?php echo e($currentDatePercent); ?>%">
                            <div class="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
                            <div class="absolute -top-8 -left-8 text-xs text-red-600 font-medium whitespace-nowrap">
                                Hari ini
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Project Rows -->
                <div class="space-y-3">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $timelineData['projectsData']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center space-x-4">
                            <!-- Project Info -->
                            <div class="w-64 flex-shrink-0">
                                <div class="flex items-center space-x-2">
                                    <?php
                                        $healthIcon = match($project['health']) {
                                            'on_track' => 'heroicon-m-check-circle',
                                            'at_risk' => 'heroicon-m-exclamation-triangle',
                                            'behind' => 'heroicon-m-clock',
                                            'overdue' => 'heroicon-m-x-circle',
                                            'completed' => 'heroicon-m-check-badge',
                                            default => 'heroicon-m-question-mark-circle',
                                        };

                                        $healthColor = match($project['health']) {
                                            'on_track' => 'text-green-600',
                                            'at_risk' => 'text-yellow-600',
                                            'behind' => 'text-orange-600',
                                            'overdue' => 'text-red-600',
                                            'completed' => 'text-blue-600',
                                            default => 'text-gray-600',
                                        };
                                    ?>
                                    <?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $healthIcon] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\DynamicComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 '.e($healthColor).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
                                    <div class="min-w-0 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                            <?php echo e($project['name']); ?>

                                        </h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                            <?php echo e($project['customer']); ?>

                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Timeline Bar -->
                            <div class="flex-1 relative h-8">
                                <div class="absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded"></div>

                                <!-- Project Duration Bar -->
                                <div class="absolute top-1 bottom-1 <?php echo e($project['color']['bg']); ?> rounded <?php echo e($project['color']['border']); ?> border"
                                     style="left: <?php echo e($project['startPercent']); ?>%; width: <?php echo e($project['widthPercent']); ?>%">

                                    <!-- Progress Bar -->
                                    <div class="h-full <?php echo e($project['color']['progress']); ?> rounded-l"
                                         style="width: <?php echo e($project['progress']); ?>%"></div>

                                    <!-- Progress Text -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xs font-medium text-white">
                                            <?php echo e($project['progress']); ?>%
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Project Stats -->
                            <div class="w-32 flex-shrink-0 text-right">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?php echo e($project['completed_tasks']); ?>/<?php echo e($project['tasks_count']); ?>

                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    <!--[if BLOCK]><![endif]--><?php if($project['daysRemaining'] < 0): ?>
                                        <?php echo e(abs($project['daysRemaining'])); ?> hari terlambat
                                    <?php elseif($project['daysRemaining'] == 0): ?>
                                        Deadline hari ini
                                    <?php else: ?>
                                        <?php echo e($project['daysRemaining']); ?> hari tersisa
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Legend -->
                <div class="flex items-center justify-center space-x-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">On Track</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">At Risk</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-orange-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">Behind</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">Overdue</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-0.5 h-3 bg-red-500"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">Hari ini</span>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="text-center py-8">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('heroicon-o-calendar-days'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(BladeUI\Icons\Components\Svg::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-12 h-12 text-gray-400 mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                <p class="text-gray-500 dark:text-gray-400">Tidak ada kegiatan untuk ditampilkan</p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/widgets/project-timeline-widget.blade.php ENDPATH**/ ?>