<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\UserResource\Pages;
use Illuminate\Support\Facades\Hash;
use App\Traits\HasExportActions;
use App\Exports\UserExport;

class UserResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Users';

    protected static ?string $slug = 'user-list';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Nama Pengguna')
                    ->required(),

                TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true),

                TextInput::make('password')
                    ->label('Password')
                    ->password()
                    ->required(fn($record) => ! $record)
                    ->dehydrated(fn($state) => filled($state))
                    ->dehydrateStateUsing(fn($state) => Hash::make($state)),

                Forms\Components\Select::make('role')
                    ->label('Role')
                    ->options(\App\Enums\UserRole::options())
                    ->required()
                    ->default(\App\Enums\UserRole::KARYAWAN->value),

                Forms\Components\Placeholder::make('karyawan_info')
                    ->label('Karyawan Terkait')
                    ->content(function ($record) {
                        if (!$record) {
                            return 'User baru - belum ada karyawan terkait';
                        }

                        $karyawan = \App\Models\Karyawan::where('id_user', $record->id)->first();

                        if ($karyawan) {
                            return "✅ {$karyawan->nama_lengkap} (NIP: {$karyawan->nip})";
                        }

                        return $record->role === 'karyawan' ?
                            '❌ Belum terkait dengan karyawan' :
                            '➖ Bukan role karyawan';
                    })
                    ->helperText('Untuk mengubah kaitan karyawan, gunakan tombol "Kaitkan dengan Karyawan" atau "Putuskan Kaitan" di halaman edit user.'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Pengguna')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('role')
                    ->label('Role')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'admin' => 'danger',
                        'supervisor' => 'warning',
                        'karyawan' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => ucfirst($state))
                    ->sortable(),

                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan Terkait')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Tidak ada')
                    ->formatStateUsing(function ($state, $record) {
                        if (!$state) {
                            return $record->role === 'karyawan' ?
                                '❌ Belum terkait' :
                                '➖ Bukan karyawan';
                        }

                        // Get the karyawan's NIP for additional info
                        $karyawan = $record->karyawan;
                        return $state . ($karyawan && $karyawan->nip ? ' (NIP: ' . $karyawan->nip . ')' : '');
                    })
                    ->color(function ($state, $record) {
                        if (!$state && $record->role === 'karyawan') {
                            return 'danger'; // Red for karyawan without link
                        }
                        if ($state) {
                            return 'success'; // Green for linked
                        }
                        return 'gray'; // Gray for non-karyawan
                    }),

                TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label('Role')
                    ->options([
                        'admin' => 'Admin',
                        'supervisor' => 'Supervisor',
                        'karyawan' => 'Karyawan',
                    ]),

                Tables\Filters\SelectFilter::make('karyawan_status')
                    ->label('Status Karyawan')
                    ->options([
                        'linked' => 'Sudah Terkait',
                        'unlinked' => 'Belum Terkait',
                        'non_karyawan' => 'Bukan Karyawan',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'] === 'linked',
                            fn(Builder $query): Builder => $query->whereHas('karyawan'),
                        )->when(
                            $data['value'] === 'unlinked',
                            fn(Builder $query): Builder => $query->where('role', 'karyawan')->whereDoesntHave('karyawan'),
                        )->when(
                            $data['value'] === 'non_karyawan',
                            fn(Builder $query): Builder => $query->where('role', '!=', 'karyawan'),
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(UserExport::class, 'Data User'),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->with(['karyawan']);
    }
}
