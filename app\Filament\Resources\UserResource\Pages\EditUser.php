<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\Karyawan;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('linkToKaryawan')
                ->label('Kaitkan dengan Karyawan')
                ->icon('heroicon-o-link')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\Select::make('karyawan_id')
                        ->label('Karyawan')
                        ->options(function () {
                            // Get karyawan that are either:
                            // 1. Not linked to any user (id_user is NULL or 0)
                            // 2. Currently linked to this specific user
                            // Get all karyawan and show their link status
                            $karyawanList = [];
                            $allKaryawan = Karyawan::with('user:id,name')->get();

                            foreach ($allKaryawan as $karyawan) {
                                $label = $karyawan->nama_lengkap . ' (NIP: ' . $karyawan->nip . ')';

                                if ($karyawan->id_user && $karyawan->id_user !== $this->record->id) {
                                    // Already linked to another user
                                    $linkedUserName = $karyawan->user ? $karyawan->user->name : 'Unknown User';
                                    $label .= ' - [Sudah terkait dengan: ' . $linkedUserName . ']';
                                } elseif ($karyawan->id_user === $this->record->id) {
                                    // Currently linked to this user
                                    $label .= ' - [Saat ini terkait]';
                                } else {
                                    // Available for linking
                                    $label .= ' - [Tersedia]';
                                }

                                $karyawanList[$karyawan->id] = $label;
                            }

                            return $karyawanList;
                        })
                        ->searchable()
                        ->preload()
                        ->required(),
                ])
                ->action(function (array $data): void {
                    try {
                        DB::beginTransaction();

                        $karyawan = Karyawan::findOrFail($data['karyawan_id']);

                        // Check if karyawan is already linked to another user
                        if ($karyawan->id_user && $karyawan->id_user !== $this->record->id) {
                            DB::rollBack();

                            // Get the linked user's name for better error message
                            $linkedUser = \App\Models\User::find($karyawan->id_user);
                            $linkedUserName = $linkedUser ? $linkedUser->name : 'Unknown User';

                            Notification::make()
                                ->title('Karyawan Sudah Terkait ⚠️')
                                ->body("Karyawan '{$karyawan->nama_lengkap}' sudah terkait dengan user '{$linkedUserName}'. Silakan pilih karyawan lain atau putuskan kaitan tersebut terlebih dahulu.")
                                ->danger()
                                ->persistent()
                                ->actions([
                                    \Filament\Notifications\Actions\Action::make('ok')
                                        ->label('Mengerti')
                                        ->close(),
                                ])
                                ->send();
                            return;
                        }

                        $karyawan->update(['id_user' => $this->record->id]);

                        DB::commit();

                        Notification::make()
                            ->title('Berhasil mengaitkan dengan karyawan')
                            ->body('Pengguna berhasil dikaitkan dengan karyawan ' . $karyawan->nama_lengkap)
                            ->success()
                            ->send();

                        $this->redirect(UserResource::getUrl('edit', ['record' => $this->record]));
                    } catch (\Exception $e) {
                        DB::rollBack();

                        Notification::make()
                            ->title('Gagal mengaitkan dengan karyawan')
                            ->body('Terjadi kesalahan: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(function () {
                    // Only show for users with role 'karyawan' who don't have a linked karyawan record
                    if ($this->record->role !== 'karyawan') {
                        return false;
                    }

                    // Check if user already has a linked karyawan
                    $hasKaryawan = Karyawan::where('id_user', $this->record->id)->exists();
                    return !$hasKaryawan;
                }),

            Actions\Action::make('unlinkFromKaryawan')
                ->label('Putuskan Kaitan')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->requiresConfirmation()
                ->action(function (): void {
                    try {
                        DB::beginTransaction();

                        // Find the linked karyawan using direct query
                        $karyawan = Karyawan::where('id_user', $this->record->id)->first();

                        if ($karyawan) {
                            $karyawanName = $karyawan->nama_lengkap;
                            $karyawan->update(['id_user' => null]);

                            Notification::make()
                                ->title('Berhasil memutuskan kaitan')
                                ->body('Pengguna berhasil diputuskan kaitannya dengan karyawan ' . $karyawanName)
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Tidak ada karyawan terkait')
                                ->body('Pengguna ini tidak terkait dengan karyawan manapun')
                                ->warning()
                                ->send();
                        }

                        DB::commit();

                        $this->redirect(UserResource::getUrl('edit', ['record' => $this->record]));
                    } catch (\Exception $e) {
                        DB::rollBack();

                        Notification::make()
                            ->title('Gagal memutuskan kaitan')
                            ->body('Terjadi kesalahan: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(function () {
                    // Only show for users with role 'karyawan' who have a linked karyawan record
                    if ($this->record->role !== 'karyawan') {
                        return false;
                    }

                    // Check if user has a linked karyawan
                    $hasKaryawan = Karyawan::where('id_user', $this->record->id)->exists();
                    return $hasKaryawan;
                }),

            Actions\Action::make('viewKaryawanInfo')
                ->label('Info Karyawan')
                ->icon('heroicon-o-information-circle')
                ->color('info')
                ->action(function () {
                    $karyawan = Karyawan::where('id_user', $this->record->id)->first();

                    if ($karyawan) {
                        Notification::make()
                            ->title('Info Karyawan Terkait 👤')
                            ->body("User '{$this->record->name}' terkait dengan karyawan: {$karyawan->nama_lengkap} (NIP: {$karyawan->nip})")
                            ->info()
                            ->persistent()
                            ->actions([
                                \Filament\Notifications\Actions\Action::make('ok')
                                    ->label('OK')
                                    ->close(),
                            ])
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Tidak Ada Kaitan 🔗')
                            ->body("User '{$this->record->name}' belum terkait dengan karyawan manapun.")
                            ->warning()
                            ->persistent()
                            ->actions([
                                \Filament\Notifications\Actions\Action::make('ok')
                                    ->label('OK')
                                    ->close(),
                            ])
                            ->send();
                    }
                })
                ->visible(fn() => $this->record->role === 'karyawan'),

            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index'); // Kembali ke halaman tabel setelah mengedit data
    }
}
